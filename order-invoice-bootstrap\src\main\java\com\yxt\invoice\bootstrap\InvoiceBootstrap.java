package com.yxt.invoice.bootstrap;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableFeignClients(basePackages = {"com.yxt.invoice"})
@ComponentScan(basePackages = {"com.yxt.invoice"})
@Slf4j
@EnableAsync
public class InvoiceBootstrap {

  public static void main(String[] args) {
    SpringApplication.run(InvoiceBootstrap.class, args);
    log.info("DddBootstrap 服务启动成功");
  }
}