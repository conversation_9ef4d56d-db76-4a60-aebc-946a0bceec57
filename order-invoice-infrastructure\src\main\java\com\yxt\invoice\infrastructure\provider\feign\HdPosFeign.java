package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.provider.dto.req.GetPosOrderAboutInvoiceReqDto;
import com.yxt.invoice.infrastructure.provider.dto.res.GetPosOrderAboutInvoiceResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.TaxCloudResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 海典Pos接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(value = "hd-pos-service")
public interface HdPosFeign {


    @PostMapping("/getPosOrderAboutInvoiceResDto") // todo notey
    TaxCloudResponse<GetPosOrderAboutInvoiceResDto> getPosOrderAboutInvoiceResDto(@RequestBody GetPosOrderAboutInvoiceReqDto req);


}
